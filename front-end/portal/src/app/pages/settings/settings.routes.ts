import { Routes } from '@angular/router';
import { SettingsComponent } from './settings.component';

export const routes: Routes = [
  {
    path: '',
    component: SettingsComponent,
    children: [
      // {
      //   path: '',
      //   pathMatch: 'full',
      //   loadComponent: () =>
      //     import('./pages/general/general.component').then(
      //       (c) => c.GeneralComponent,
      //     ),
      // },
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'users',
      },
      {
        path: 'users',
        loadChildren: () =>
          import('./pages/users/users.routes').then((r) => r.routes),
      },
      {
        path: 'google',
        loadComponent: () =>
          import('./pages/google/google.component').then(
            (c) => c.GoogleComponent,
          ),
      },
      {},
    ],
  },
];
