{"general": {"search": "Search...", "select": "Select", "buttons": {"save": "Save", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "create": "Create", "update": "Update", "complete": "Complete", "review": "Review", "reset": "Reset", "add": "Add", "overview": "Overview", "accept": "Accept", "reject": "Reject", "edit": "Edit", "done": "Done", "preview": "Preview", "share": "Share", "back_to_portal": "Back to portal", "next": "Next", "previous": "Previous", "mark_complete": "<PERSON><PERSON>", "close": "Close", "change_selection": "Change selection", "mark_as_complete": "Mark as complete", "remove": "Remove", "retry": "Retry", "refresh": "Refresh", "send": "Send", "login": "<PERSON><PERSON>", "register": "Register", "select_all": "Select all", "deselect_all": "Deselect all", "open": "Open"}, "selected_all": "All selected", "unknown": "Unknown", "back": "Back", "toasts": {"error": {"title": "Oops, something went wrong!", "description": "It looks like something went wrong on our end. Please try again or contact our support."}, "success": {"title": "Success!", "description": "The action you performed has been successfully completed."}}, "bool": {"true": "Yes", "false": "No"}, "manage": "Manage", "empty_data": "It looks like there is no data available.", "from": "From", "until": "Until", "to": "To", "filter": "Filter", "sort": "Sort by", "settings": "Settings", "request_support": "Request support", "date": "Date", "google": "Google", "microsoft": "Microsoft", "facebook": "Facebook", "linkedin": "LinkedIn", "pinterest": "Pinterest", "tiktok": "TikTok", "instagram": "Instagram", "connected": "Connected", "error": "Error", "preview_mode": "Preview mode. Open in incognito to exit preview mode.", "skipped": "Skipped", "skip": "<PERSON><PERSON>", "synced": "Synced", "connect": "Connect", "failed": "Failed", "failed_data": "Successful", "success": "Success", "pending": "Pending", "connect_your_accounts": "Connect your accounts", "add_another": "Add another one", "skipped_drawer": {"title": "Connection skipped!", "description": "It looks like you have skipped this connection."}, "syncing": "Syncing", "empty_placeholder": "There are no entry's found"}, "pages": {"auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "password": "Password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "no_account": "Don't have an account?", "register": "Register now"}, "register": {"title": "Register", "name": "Company Name", "name_required": "Company name is required", "firstname": "First Name", "firstname_required": "First name is required", "lastname": "Last Name", "lastname_required": "Last name is required", "email": "Email", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "password": "Password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "already_account": "Already have an account?", "login": "Login now"}}, "settings": {"sidebar": {"general": "General", "users": "Users", "google": "Google", "integrations": "Integrations", "add-ons": "Add-ons"}, "users": {"index": {"table": {"name": "Name", "email": "Email", "role": "Role"}, "add": "Add user"}, "detail": {"inputs": {"role": "Role", "firstname": "Firstname", "lastname": "Lastname", "image": "Image", "email": "Email"}}}, "google": {"table": {"name": "Name", "email": "Email"}, "delete": {"title": "Delete Google account?", "description": "Are you sure you want to delete this Google account?"}}, "add-ons": {}}, "dashboards": {"index": {"table": {"name": "Name", "created_at": "Created at", "last_change_at": "Last change at"}, "add": "Add dashboard", "delete": {"title": "Delete dashboard?", "description": "Are you sure you want to delete this dashboard?"}, "share": {"title": "Share"}}, "detail": {"steps": {"GENERAL": "General", "PAGES": "Pages", "SOURCES": "Data sources", "RECIPIENT": "Recipient", "KPIS": "KPIs"}, "general": {"title": "Dashboard configuration", "description": "Set up your dashboard template and display options.", "fields": {"name": {"title": "Dashboard title", "description": "This will be displayed at the top of your dashboard."}, "user": {"title": "Assigned employee", "description": "Employee responsible for managing this dashboard."}, "business_type": {"title": "Business type"}}}, "sources": {"title": "Data Sources", "description": "Configure your Google Ads accounts and other data sources", "google_ad_accounts": {"field": "Google ad accounts", "button": "Add Google Ad account"}}, "pages": {"pages": {"EXECUTIVE": "Executive", "TRENDS": "Trends", "METRIC_TREE": "Metric tree", "INSIGHTS": "Insights"}, "sections": {"EXECUTIVE_SUMMARY": "Executive summary", "EXECUTIVE_OVERVIEW": "Executive overview", "PERFORMANCE_TRENDS": "Performance trends", "CAMPAIGN_OVERVIEW": "Campaign overview", "PERIOD_ANALYSIS": "Period analysis", "METRIC_INFLUENCE": "Metric influence"}}, "recipient": {"title": "Client information", "description": "Configure client details and branding for the dashboard", "fields": {"company": "Client name"}}, "kpis": {"types": {"MONTHLY_GROW_TARGET": "Monthly grow target", "EFFICIENCY_TARGET": "Efficiency target", "TOTAL_CONVERSION_VALUE": "Total conversion value", "ROAS": "ROAS"}, "target": "Target", "unit": "Unit", "year": "Year", "units": {"percentage": "Percentage", "amount": "Amount"}, "add": "Add kpi"}}}}, "layouts": {"container": {"settings": "Settings", "home": "Home", "dashboards": "Dashboards"}}, "components": {"pagination": {"showing": "Showing", "to": "to", "results": "results", "previous": "Previous", "of": "of", "next": "Next"}}, "enums": {"user-role": {"ADMIN": "Admin", "USER": "User"}, "business-type": {"LEAD_GENERATION": "Lead Generation", "SAAS": "SaaS", "E_COMMERCE": "E-commerce"}}}